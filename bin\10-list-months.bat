@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

set "MONTHS_ALL=%BASE_DIR%months.txt"
set "MONTHS_TODO=%BASE_DIR%months_to_migrate.txt"

REM 先用 PowerShell（若存在）精准生成并排除最近 N 个月
where powershell >nul 2>nul
if %errorlevel%==0 (
  echo 使用 PowerShell 生成月份清单...
  REM 创建临时 PowerShell 脚本文件来避免转义问题
  echo Get-ChildItem -Directory "!SOURCE_ROOT!" ^| Where-Object { $_.Name -match "^[0-9]{4}-[0-9]{2}$" } ^| Sort-Object Name ^| Select-Object -ExpandProperty Name ^| Out-File "!MONTHS_ALL!" -Encoding UTF8 > "!TEMP!\list_months.ps1"
  powershell -NoProfile -ExecutionPolicy Bypass -File "!TEMP!\list_months.ps1"
  echo Get-Content "!MONTHS_ALL!" ^| Select-Object -SkipLast !EXCLUDE_RECENT_MONTHS! ^| Out-File "!MONTHS_TODO!" -Encoding UTF8 > "!TEMP!\skip_months.ps1"
  powershell -NoProfile -ExecutionPolicy Bypass -File "!TEMP!\skip_months.ps1"
  del "!TEMP!\list_months.ps1" "!TEMP!\skip_months.ps1" >nul 2>nul
) else (
  echo 未检测到 PowerShell，退化到 CMD 方式（僅生成 months.txt）。
  dir /ad /b "%SOURCE_ROOT%" | findstr /r "^[0-9][0-9][0-9][0-9]-[0-1][0-9]$" > "%MONTHS_ALL%"
  if not exist "%MONTHS_TODO%" (
    echo 请人工编辑 %MONTHS_TODO% ，从 %MONTHS_ALL% 复制需要迁移的月份（建议排除最近 %EXCLUDE_RECENT_MONTHS% 个月）。
    copy "%MONTHS_ALL%" "%MONTHS_TODO%" >nul
  )
)

echo 已生成：
for %%f in ("%MONTHS_ALL%","%MONTHS_TODO%") do if exist %%~f echo  ^> %%~f

echo. & echo 请检查 months_to_migrate.txt 是否符合预期。
exit /b 0